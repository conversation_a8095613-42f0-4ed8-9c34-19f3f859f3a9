// src/utils/cloudbaseTest.ts
// 用于测试Cloudbase连接的工具函数

import { CloudbaseManager } from '../cloudbase';

export interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp: Date;
}

export class CloudbaseTest {
  private static results: TestResult[] = [];

  // 测试网络连接
  static async testNetworkConnectivity(): Promise<TestResult> {
    const startTime = Date.now();
    try {
      // 使用私有方法测试网络连接
      const isConnected = await (CloudbaseManager as any).checkNetworkConnectivity();
      const duration = Date.now() - startTime;
      
      const result: TestResult = {
        success: isConnected,
        message: isConnected ? `网络连接正常 (${duration}ms)` : '网络连接失败',
        details: { duration, timestamp: new Date() },
        timestamp: new Date()
      };
      
      this.results.push(result);
      return result;
    } catch (e) {
      const result: TestResult = {
        success: false,
        message: `网络测试异常: ${e instanceof Error ? e.message : String(e)}`,
        details: { error: e },
        timestamp: new Date()
      };
      
      this.results.push(result);
      return result;
    }
  }

  // 测试SDK初始化
  static async testSDKInitialization(): Promise<TestResult> {
    const startTime = Date.now();
    try {
      await CloudbaseManager.clearInstance();
      const instance = await CloudbaseManager.getInstance();
      const duration = Date.now() - startTime;
      
      const result: TestResult = {
        success: !!instance,
        message: instance ? `SDK初始化成功 (${duration}ms)` : 'SDK初始化失败',
        details: { duration, hasInstance: !!instance },
        timestamp: new Date()
      };
      
      this.results.push(result);
      return result;
    } catch (e) {
      const result: TestResult = {
        success: false,
        message: `SDK初始化异常: ${e instanceof Error ? e.message : String(e)}`,
        details: { error: e },
        timestamp: new Date()
      };
      
      this.results.push(result);
      return result;
    }
  }

  // 测试匿名登录
  static async testAnonymousLogin(): Promise<TestResult> {
    const startTime = Date.now();
    try {
      const loginResult = await CloudbaseManager.anonymousLogin();
      const duration = Date.now() - startTime;
      
      const result: TestResult = {
        success: !!loginResult,
        message: loginResult ? `匿名登录成功 (${duration}ms)` : '匿名登录失败',
        details: { duration, loginResult },
        timestamp: new Date()
      };
      
      this.results.push(result);
      return result;
    } catch (e) {
      const result: TestResult = {
        success: false,
        message: `匿名登录异常: ${e instanceof Error ? e.message : String(e)}`,
        details: { error: e },
        timestamp: new Date()
      };
      
      this.results.push(result);
      return result;
    }
  }

  // 运行完整测试套件
  static async runFullTest(): Promise<TestResult[]> {
    console.log('开始运行Cloudbase完整测试...');
    this.results = []; // 清空之前的结果
    
    const tests = [
      { name: '网络连接测试', test: this.testNetworkConnectivity },
      { name: 'SDK初始化测试', test: this.testSDKInitialization },
      { name: '匿名登录测试', test: this.testAnonymousLogin },
    ];
    
    for (const { name, test } of tests) {
      console.log(`运行 ${name}...`);
      const result = await test.call(this);
      console.log(`${name} ${result.success ? '✅' : '❌'}: ${result.message}`);
      
      // 如果某个测试失败，等待一段时间再继续
      if (!result.success) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    return this.results;
  }

  // 获取测试结果摘要
  static getTestSummary(): string {
    const total = this.results.length;
    const passed = this.results.filter(r => r.success).length;
    const failed = total - passed;
    
    const summary = [
      '=== Cloudbase 测试摘要 ===',
      `总测试数: ${total}`,
      `通过: ${passed}`,
      `失败: ${failed}`,
      `成功率: ${total > 0 ? Math.round((passed / total) * 100) : 0}%`,
      '',
      '详细结果:',
      ...this.results.map((r, i) => 
        `${i + 1}. ${r.success ? '✅' : '❌'} ${r.message} (${r.timestamp.toLocaleTimeString()})`
      )
    ];
    
    return summary.join('\n');
  }

  // 清空测试结果
  static clearResults(): void {
    this.results = [];
  }
}

export default CloudbaseTest;
