// src/utils/cloudbase.ts
import app, { auth } from '../cloudbase'; // 引入我们配置好的实例

/**
 * 通用的云函数调用工具
 * @param name - 云函数的名称
 * @param data - 需要传递给云函数的数据
 * @returns Promise<any>
 */
export const callFunction = async (name: string, data: object = {}): Promise<any> => {
  // 确保用户已登录，获取登录状态
  const loginState = await auth.getLoginState();

  if (!loginState) {
    console.log('用户未登录，尝试匿名登录...');
    // 如果没有登录态，可以先进行匿名登录，以获得调用权限
    await auth.signInAnonymously();
  }
  
  // 发起云函数调用
  try {
    const res = await app.callFunction({
      name,
      data,
    });
    
    // 我们期望云函数总是返回 { code, message, data } 的格式
    if (res.result && res.result.code !== 200) {
        throw new Error(res.result.message || `云函数[${name}]执行失败`);
    }
    return res.result;

  } catch (e: any) {
      console.error(`调用云函数[${name}]失败:`, e);
      // 将云函数执行的错误统一格式化抛出
      throw new Error(e.message || `云函数[${name}]调用异常`);
  }
};
