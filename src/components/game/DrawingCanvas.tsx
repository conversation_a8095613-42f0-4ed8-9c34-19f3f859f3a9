// src/components/game/DrawingCanvas.tsx
// 职责：处理所有与绘画相关的逻辑，包括手势捕捉、路径生成和数据同步。

import React, 'useState', useRef } from 'react';
import { View, StyleSheet } from 'react-native';
import { Svg, Path } from 'react-native-svg';
import { PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import { db } from '../../cloudbase';
import _ from 'lodash';

interface DrawingCanvasProps {
  roomId: string;
  isDrawer: boolean;
  pathsData: string[]; // 从GameScreen传入的路径数据
}

const DrawingCanvas = ({ roomId, isDrawer, pathsData }: DrawingCanvasProps) => {
  const [currentPath, setCurrentPath] = useState<string>('');
  
  // 使用useRef来存储路径数组，避免不必要的重渲染
  const pathsRef = useRef<string[]>(pathsData || []);
  
  // 创建一个防抖函数，每200ms最多向数据库同步一次数据，避免过于频繁的写入
  const debouncedSync = useRef(_.debounce((newPaths: string[]) => {
    db.collection('rooms').doc(roomId).update({
      'gameData.drawingData.paths': newPaths
    });
  }, 200)).current;

  const onGestureEvent = (event: PanGestureHandlerGestureEvent) => {
    if (!isDrawer) return;

    const { x, y } = event.nativeEvent;
    // 如果是路径的起点，使用'M' (Move to)，否则使用'L' (Line to)
    const point = `${currentPath === '' ? 'M' : 'L'}${x.toFixed(0)},${y.toFixed(0)}`;
    setCurrentPath(prev => prev + point);
  };

  const onGestureEnd = () => {
    if (!isDrawer || !currentPath) return;

    // 将当前完成的路径添加到路径数组中
    const newPaths = [...pathsRef.current, currentPath];
    pathsRef.current = newPaths;
    
    // 清空当前路径，准备下一次绘画
    setCurrentPath('');
    
    // 通过防抖函数同步到数据库
    debouncedSync(newPaths);
  };

  const renderPaths = pathsData || [];

  return (
    <View style={styles.container}>
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onEnded={onGestureEnd}
        enabled={isDrawer}
      >
        <View style={styles.canvas}>
          <Svg width="100%" height="100%">
            {/* 渲染从数据库同步过来的所有历史路径 */}
            {renderPaths.map((path, index) => (
              <Path
                key={`path-${index}`}
                d={path}
                stroke="black"
                strokeWidth={3}
                fill="none"
              />
            ))}
            {/* 实时渲染当前正在画的路径 */}
            {isDrawer && currentPath ? (
              <Path
                d={currentPath}
                stroke="black"
                strokeWidth={3}
                fill="none"
              />
            ) : null}
          </Svg>
        </View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  canvas: {
    flex: 1,
  },
});

export default DrawingCanvas;

