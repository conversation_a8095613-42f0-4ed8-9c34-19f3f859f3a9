// src/screens/RoomScreen.tsx
// 职责：构建一个能够实时同步玩家列表、并区分房主权限的动态房间大厅。

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, Button, ActivityIndicator, Alert } from 'react-native';
import { db, auth } from '../cloudbase'; // 直接导入db和auth实例
import { callFunction } from '../utils/cloudbase';

// 定义玩家和房间的数据结构类型
interface Player {
  openid: string;
  nickName: string;
  avatarUrl: string;
  score: number;
}

interface Room {
  _id: string;
  hostId: string;
  players: Player[];
  status: 'waiting' | 'playing' | 'finished';
}

const RoomScreen = ({ route, navigation }: any) => {
  const { roomId } = route.params;
  const [room, setRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);
  const currentUser = auth.currentUser;

  useEffect(() => {
    if (!roomId) return;

    // 设置数据库监听
    const listener = db.collection('rooms').doc(roomId).watch({
      onChange: (snapshot) => {
        if (snapshot.docs.length > 0) {
          const newRoomData = snapshot.docs[0] as Room;
          setRoom(newRoomData);

          // 新增逻辑：如果房间状态变为'playing'，则跳转到游戏界面
          if (newRoomData.status === 'playing') {
            navigation.replace('Game', { roomId: newRoomData._id });
          }

        } else {
          // 房间可能被解散了
          Alert.alert('提示', '房间已解散', [{ text: 'OK', onPress: () => navigation.navigate('Home') }]);
        }
        setLoading(false);
      },
      onError: (err) => {
        console.error('监听失败', err);
        Alert.alert('错误', '无法连接到房间，请重试', [{ text: 'OK', onPress: () => navigation.navigate('Home') }]);
        setLoading(false);
      }
    });

    // 组件卸载时，关闭监听，防止内存泄漏
    return () => {
      listener.close();
    };
  }, [roomId, navigation]);

  const handleStartGame = async () => {
    setLoading(true);
    try {
      await callFunction('startGame', { roomId });
      // 成功调用后，房间状态会通过监听自动更新，无需手动跳转或处理
    } catch (e: any) {
      Alert.alert('错误', e.message);
    } finally {
      setLoading(false);
    }
  };

  const renderPlayerItem = ({ item }: { item: Player }) => (
    <View style={styles.playerItem}>
      {/* <Image source={{ uri: item.avatarUrl }} style={styles.avatar} /> */}
      <Text style={styles.playerName}>{item.nickName}</Text>
      {item.openid === room?.hostId && <Text style={styles.hostTag}>房主</Text>}
    </View>
  );

  if (loading) {
    return <ActivityIndicator style={styles.loading} size="large" />;
  }

  if (!room) {
    return <Text>无法加载房间信息。</Text>;
  }
  
  const isHost = currentUser?.uid === room.hostId;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>房间大厅</Text>
      <Text style={styles.roomIdText}>房间号: {roomId}</Text>

      <FlatList
        data={room.players}
        renderItem={renderPlayerItem}
        keyExtractor={(item) => item.openid}
        style={styles.playerList}
      />

      {isHost ? (
        <Button title="开始游戏" onPress={handleStartGame} color="#7e57c2" disabled={loading} />
      ) : (
        <Text style={styles.waitingText}>等待房主开始游戏...</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    paddingTop: 50,
    backgroundColor: '#f5f5f5',
  },
  loading: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  roomIdText: {
    fontSize: 16,
    color: '#555',
    textAlign: 'center',
    marginBottom: 20,
  },
  playerList: {
    flex: 1,
    marginBottom: 20,
  },
  playerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 15,
  },
  playerName: {
    fontSize: 18,
    fontWeight: '500',
    flex: 1,
  },
  hostTag: {
    fontSize: 14,
    color: '#7e57c2',
    fontWeight: 'bold',
  },
  waitingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#888',
    padding: 20,
  }
});

export default RoomScreen;

