// src/screens/GameScreen.tsx
// 职责：构建功能完整的“你画我猜”游戏主界面，实现实时绘画、猜测和裁判功能。

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TextInput, Button, Alert } from 'react-native';
import { db, auth } from '../cloudbase';
import { callFunction } from '../utils/cloudbase';
// import DrawingCanvas from '../components/game/DrawingCanvas'; // 我们将创建一个画板组件

const GameScreen = ({ route, navigation }: any) => {
  const { roomId } = route.params;
  const [room, setRoom] = useState<any>(null);
  const [guess, setGuess] = useState('');
  const currentUser = auth.currentUser;

  useEffect(() => {
    const listener = db.collection('rooms').doc(roomId).watch({
      onChange: (snapshot) => {
        if (snapshot.docs.length > 0) {
          const newRoomData = snapshot.docs[0];
          setRoom(newRoomData);
          if (newRoomData.status === 'finished') {
            Alert.alert('游戏结束', '返回大厅', [{ text: 'OK', onPress: () => navigation.replace('Home') }]);
          }
        }
      },
      onError: console.error
    });
    return () => listener.close();
  }, [roomId, navigation]);
  
  const isDrawer = currentUser?.uid === room?.gameData?.currentDrawer;

  const handleSubmitGuess = async () => {
    if (!guess.trim()) return;
    try {
      await callFunction('submitGuess', { roomId, guess: guess.trim() });
      setGuess('');
    } catch (e: any) {
      Alert.alert('错误', e.message);
    }
  };

  const handleJudge = async (correctGuessOpenId: string) => {
     try {
       // 此处可以增加一个确认弹窗
       await callFunction('judgeAndScore', { roomId, correctGuessOpenId });
       // 成功后，监听会自动更新房间到下一轮
     } catch (e: any) {
       Alert.alert('错误', e.message);
     }
  };

  const renderGuessItem = ({ item }: any) => (
    <Text style={styles.guessText}>{item.nickName}: {item.guess}</Text>
  );

  if (!room) {
    return <View style={styles.container}><Text>正在加载游戏...</Text></View>;
  }
  
  // 裁判界面
  if (room.status === 'judging' && isDrawer) {
    return (
        <View style={styles.container}>
            <Text style={styles.title}>请选择正确答案</Text>
            <FlatList
                data={room.gameData.guesses}
                keyExtractor={(item) => item.openid}
                renderItem={({ item }) => (
                    <View style={styles.judgeItem}>
                        <Text style={styles.judgeText}>{item.nickName}: {item.guess}</Text>
                        <Button title="就是这个" onPress={() => handleJudge(item.openid)} />
                    </View>
                )}
            />
            <Button title="没有人猜对" onPress={() => handleJudge('')} color="gray" />
        </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.word}>
          {isDrawer ? `你要画的词是: ${room.gameData.currentWord}` : '猜猜看这是什么?'}
        </Text>
      </View>

      <View style={styles.canvasContainer}>
        {/* TODO: 替换为真实的画板组件 */}
        {/* <DrawingCanvas isDrawer={isDrawer} roomId={roomId} /> */}
        <Text>这里是画板区域</Text>
      </View>
      
      <View style={styles.chatContainer}>
          <FlatList
            data={room.gameData.guesses}
            renderItem={renderGuessItem}
            keyExtractor={(item, index) => `${item.openid}-${index}`}
          />
      </View>

      {!isDrawer && (
        <View style={styles.inputContainer}>
          <TextInput
            style={styles.input}
            value={guess}
            onChangeText={setGuess}
            placeholder="输入你的答案"
          />
          <Button title="提交" onPress={handleSubmitGuess} />
        </View>
      )}
    </View>
  );
};

// ...样式代码...
const styles = StyleSheet.create({
    container: { flex: 1, padding: 10, paddingTop: 40 },
    header: { padding: 10, alignItems: 'center' },
    word: { fontSize: 18, fontWeight: 'bold' },
    canvasContainer: { flex: 1, borderWidth: 1, borderColor: '#ccc', marginVertical: 10, justifyContent: 'center', alignItems: 'center' },
    chatContainer: { height: 150, borderWidth: 1, borderColor: '#eee', padding: 5 },
    guessText: { fontSize: 14 },
    inputContainer: { flexDirection: 'row', padding: 5 },
    input: { flex: 1, borderWidth: 1, borderColor: 'gray', marginRight: 10, padding: 8, borderRadius: 5 },
    title: { fontSize: 24, fontWeight: 'bold', textAlign: 'center', margin: 20 },
    judgeItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', padding: 15, borderBottomWidth: 1, borderBottomColor: '#eee' },
    judgeText: { fontSize: 18 }
});

export default GameScreen;

