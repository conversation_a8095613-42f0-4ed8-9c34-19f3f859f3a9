// src/screens/HomeScreen.tsx
import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, ActivityIndicator, Alert } from 'react-native';
import { callFunction } from '../utils/cloudbase';

const HomeScreen = ({ navigation }: any) => {
  const [roomId, setRoomId] = useState('');
  const [loading, setLoading] = useState(false);

  const handleCreateRoom = async () => {
    setLoading(true);
    try {
      const result = await callFunction('createRoom');
      if (result.data?._id) {
        // 创建成功，跳转到房间，并传递房间ID
        navigation.navigate('Room', { roomId: result.data._id });
      } else {
        throw new Error('创建房间失败，未返回房间ID');
      }
    } catch (e: any) {
      Alert.alert('错误', e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleJoinRoom = async () => {
    if (!roomId.trim()) {
      Alert.alert('提示', '请输入房间号');
      return;
    }
    setLoading(true);
    try {
      const result = await callFunction('joinRoom', { roomId: roomId.trim() });
      if (result.data?._id) {
        // 加入成功，跳转到房间，并传递房间ID
        navigation.navigate('Room', { roomId: result.data._id });
      } else {
        // 自定义错误处理
        const errorMessage = result.message || '加入房间失败';
        throw new Error(errorMessage);
      }
    } catch (e: any) {
      Alert.alert('错误', e.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>团子大厅</Text>
      <Text style={styles.subtitle}>创建或加入一个房间开始游戏</Text>
      
      {loading ? (
        <ActivityIndicator size="large" color="#7e57c2" />
      ) : (
        <>
          <TextInput
            style={styles.input}
            placeholder="输入房间号"
            placeholderTextColor="#999"
            value={roomId}
            onChangeText={setRoomId}
            maxLength={6} // 通常房间号不会太长
          />
          <View style={styles.buttonContainer}>
            <Button
              title="加入房间"
              onPress={handleJoinRoom}
              disabled={!roomId.trim()}
              color="#81c784"
            />
          </View>
          <Text style={styles.orText}>或</Text>
          <View style={styles.buttonContainer}>
            <Button
              title="创建新房间"
              onPress={handleCreateRoom}
              color="#7e57c2"
            />
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 40,
  },
  input: {
    width: '80%',
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
    fontSize: 18,
    textAlign: 'center',
    backgroundColor: '#fff',
  },
  buttonContainer: {
    width: '80%',
    marginVertical: 10,
    borderRadius: 25,
    overflow: 'hidden',
  },
  orText: {
    color: '#888',
    marginVertical: 15,
    fontSize: 16,
  }
});

export default HomeScreen;

