// src/screens/LoginScreen.tsx
import React, { useState, useCallback } from 'react';
import { View, Text, Button, StyleSheet, Image, ActivityIndicator, Alert } from 'react-native';
import { callFunction, CloudbaseManager } from '../cloudbase';
import AsyncStorage from '@react-native-async-storage/async-storage';

const MAX_RETRIES = 3;
const RETRY_DELAY = 1000;
const INITIALIZATION_TIMEOUT = 15000; // 15 seconds

const LoginScreen = ({ navigation }: any) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [retryCount, setRetryCount] = useState(0);

  const initializeCloudbase = useCallback(async (retryAttempt = 0): Promise<boolean> => {
    try {
      // 先清理之前的状态
      await CloudbaseManager.clearInstance();
      
      // 尝试初始化
      await CloudbaseManager.getInstance();
      return true;
    } catch (e: any) {
      console.error(`Cloudbase initialization attempt ${retryAttempt + 1} failed:`, e);
      
      if (retryAttempt >= MAX_RETRIES - 1) {
        return false;
      }
      
      // 安全地检查错误消息
      const errorMessage = e && typeof e.message === 'string' ? e.message : '';
      const delay = errorMessage.includes('网络连接') 
        ? RETRY_DELAY * 3
        : RETRY_DELAY;
        
      await new Promise(resolve => setTimeout(resolve, delay));
      return initializeCloudbase(retryAttempt + 1);
    }
  }, []);

  const resetLoginState = useCallback(async () => {
    setLoading(false);
    setError('');
    setRetryCount(0);
    await CloudbaseManager.clearInstance();
    await AsyncStorage.removeItem('loginStatus');
    await AsyncStorage.removeItem('userInfo');
  }, []);

  const handleAnonymousLogin = async () => {
    if (loading) return;
    
    setLoading(true);
    setError('');

    try {
      // 清除之前的登录状态
      await resetLoginState();
      
      // 尝试初始化 Cloudbase
      const isInitialized = await initializeCloudbase();
      if (!isInitialized) {
        throw new Error('无法连接到服务器，请检查网络连接');
      }
      
      // 执行匿名登录
      await CloudbaseManager.anonymousLogin();

      // 生成随机昵称
      const guestId = Math.floor(Math.random() * 10000);
      const nickName = `游客${guestId}`;
      
      // 调用login云函数，完成用户注册/登录
      const loginResult = await callFunction('login', {
        userInfo: {
          nickName,
          avatarUrl: `https://placehold.co/100x100/9e9e9e/white?text=Guest${guestId}`,
        }
      });

      if (!loginResult?.result) {
        throw new Error('服务器响应异常');
      }

      if (loginResult.result.code !== 200) {
        throw new Error(loginResult.result.message || '登录失败');
      }

      // 保存登录状态
      await Promise.all([
        AsyncStorage.setItem('loginStatus', 'logged_in'),
        AsyncStorage.setItem('userInfo', JSON.stringify({
          nickName,
          openid: loginResult.result.openid,
        }))
      ]);
      
      // 导航到主页
      navigation.replace('Home');

    } catch (e: any) {
      console.error("登录失败:", e);
      
      // 清理实例，以便下次重试
      await CloudbaseManager.clearInstance();
      
      const errorMessage = e.message || '登录失败，请重试';
      setError(errorMessage);
      setLoading(false);
      
      if (retryCount < MAX_RETRIES - 1) {
        setRetryCount(prev => prev + 1);
        Alert.alert(
          '登录失败',
          `${errorMessage}\n是否要重试？`,
          [
            {
              text: '取消',
              style: 'cancel',
              onPress: resetLoginState
            },
            {
              text: '重试',
              onPress: () => {
                setTimeout(() => {
                  handleAnonymousLogin();
                }, RETRY_DELAY * 3); // 增加重试间隔
              },
            },
          ],
          { cancelable: false }
        );
      } else {
        Alert.alert(
          '登录失败', 
          '多次尝试失败，请检查网络连接后再试',
          [
            {
              text: '确定',
              onPress: resetLoginState
            }
          ],
          { cancelable: false }
        );
      }
    } finally {
      if (!error) {
        setLoading(false);
      }
    }
  };

  return (
    <View style={styles.container}>
      <Image 
        source={{ uri: 'https://placehold.co/150x150/7e57c2/white?text=Tuanzi' }} 
        style={styles.logo} 
      />
      <Text style={styles.title}>欢迎来到团子</Text>
      <Text style={styles.subtitle}>我们团，你们玩</Text>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#7e57c2" />
          <Text style={styles.loadingText}>正在登录...</Text>
          <Text style={styles.loadingSubText}>首次连接可能需要较长时间</Text>
        </View>
      ) : (
        <View style={styles.buttonContainer}>
          <Button
            title="快速开始 (游客模式)"
            onPress={handleAnonymousLogin}
            color="#757575"
          />
        </View>
      )}

      {error ? (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.errorHint}>点击按钮重试</Text>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    justifyContent: 'center', 
    alignItems: 'center', 
    backgroundColor: '#f5f5f5', 
    padding: 20 
  },
  logo: { 
    width: 150, 
    height: 150, 
    borderRadius: 75, 
    marginBottom: 40 
  },
  title: { 
    fontSize: 28, 
    fontWeight: 'bold', 
    color: '#333', 
    marginBottom: 10 
  },
  subtitle: { 
    fontSize: 16, 
    color: '#666', 
    marginBottom: 50 
  },
  buttonContainer: { 
    width: '80%', 
    borderRadius: 25, 
    overflow: 'hidden' 
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
  },
  loadingSubText: {
    marginTop: 5,
    color: '#999',
    fontSize: 12,
  },
  errorContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  errorText: {
    color: '#d32f2f',
    marginBottom: 5,
  },
  errorHint: {
    color: '#666',
    fontSize: 12,
  }
});

export default LoginScreen;

