// src/cloudbase/index.ts
import cloudbase from "@cloudbase/js-sdk";
import "@cloudbase/js-sdk/auth";
import "@cloudbase/js-sdk/functions";
import AsyncStorage from '@react-native-async-storage/async-storage';

// 定义自定义的全局对象类型
interface CustomLocation {
  protocol: string;
  host: string;
  hostname: string;
  href: string;
  origin: string;
  pathname: string;
  search: string;
  hash: string;
  port: string;
  toString: () => string;
}

interface CustomDocument {
  createElement: (tagName: string) => {
    style: Record<string, any>;
    setAttribute: (name: string, value: string) => void;
    getElementsByTagName: () => any[];
    tagName: string;
    id: string;
    className: string;
    children: any[];
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
    addEventListener: (event: string, handler: () => void) => void;
    removeEventListener: (event: string, handler: () => void) => void;
  };
  getElementsByTagName: (tagName: string) => any[];
  getElementsByClassName: (className: string) => any[];
  getElementById: (id: string) => any;
  querySelector: (selector: string) => any;
  querySelectorAll: (selector: string) => any[];
  documentElement: {
    style: Record<string, any>;
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
  };
  head: {
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
    getElementsByTagName: (tagName: string) => any[];
  };
  body: {
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
    getElementsByTagName: (tagName: string) => any[];
  };
  createTextNode: (text: string) => { nodeValue: string };
  implementation: {
    createHTMLDocument: (title: string) => CustomDocument;
  };
}

interface CustomWindow {
  location: CustomLocation;
  document: CustomDocument;
  localStorage: CustomStorage;
  navigator: {
    userAgent: string;
    language: string;
    languages: string[];
    platform: string;
  };
  addEventListener: (event: string, handler: () => void) => void;
  removeEventListener: (event: string, handler: () => void) => void;
  setTimeout: typeof global.setTimeout;
  clearTimeout: typeof global.clearTimeout;
  setInterval: typeof global.setInterval;
  clearInterval: typeof global.clearInterval;
}

interface CustomStorage {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;
  clear: () => Promise<void>;
  length: number;
  key: (index: number) => string | null;
}

// 扩展 global 类型
declare global {
  var location: CustomLocation;
  var document: CustomDocument;
  var window: CustomWindow;
  var localStorage: CustomStorage;
}

// 扩展 cloudbase 类型
interface CloudbaseAuth {
  anonymousAuthProvider: () => {
    signIn: () => Promise<any>;
  };
  signInAnonymously: () => Promise<any>;
  getLoginState: () => Promise<any>;
  signOut: () => Promise<void>;
  onLoginStateChanged: (callback: (loginState: any) => void) => void;
}

interface CloudbaseApp {
  auth: (options?: any) => CloudbaseAuth;
  callFunction: (params: { name: string; data: any }) => Promise<any>;
}

// 错误类型枚举
enum CloudbaseErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  SDK_INIT_ERROR = 'SDK_INIT_ERROR',
  AUTH_INIT_ERROR = 'AUTH_INIT_ERROR',
  LOGIN_ERROR = 'LOGIN_ERROR',
  FUNCTION_CALL_ERROR = 'FUNCTION_CALL_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 自定义错误类
class CloudbaseError extends Error {
  public type: CloudbaseErrorType;
  public originalError?: Error;
  public timestamp: Date;

  constructor(type: CloudbaseErrorType, message: string, originalError?: Error) {
    super(message);
    this.name = 'CloudbaseError';
    this.type = type;
    this.originalError = originalError;
    this.timestamp = new Date();
  }
}

// 增强的调试日志函数
const debug = (...args: any[]) => {
  if (__DEV__) {
    const timestamp = new Date().toISOString();
    console.log(`[Cloudbase ${timestamp}]`, ...args);
  }
};

const error = (...args: any[]) => {
  const timestamp = new Date().toISOString();
  console.error(`[Cloudbase Error ${timestamp}]`, ...args);
};

const warn = (...args: any[]) => {
  if (__DEV__) {
    const timestamp = new Date().toISOString();
    console.warn(`[Cloudbase Warning ${timestamp}]`, ...args);
  }
};

// 验证全局对象是否正确设置
const validateGlobalObjects = () => {
  const requiredGlobals = ['location', 'document', 'window', 'localStorage'] as const;
  const missingGlobals = requiredGlobals.filter(key => !(key in global));
  
  if (missingGlobals.length > 0) {
    debug('Missing global objects:', missingGlobals);
    return false;
  }

  if (!global.window?.document || !global.window?.location) {
    debug('Window object is missing required properties');
    return false;
  }

  return true;
};

// 验证 Cloudbase SDK 是否正确加载
const validateCloudbaseSDK = () => {
  if (typeof (cloudbase as any).init !== 'function') {
    debug('Cloudbase SDK not properly loaded: missing init method');
    return false;
  }

  if (typeof (cloudbase as any).auth !== 'function') {
    debug('Cloudbase SDK not properly loaded: missing auth module');
    return false;
  }

  return true;
};

export class CloudbaseManager {
  private static instance: CloudbaseApp | null = null;
  private static auth: CloudbaseAuth | null = null;
  private static isInitializing: boolean = false;
  private static initError: CloudbaseError | null = null;
  private static initRetryCount: number = 0;
  private static readonly MAX_RETRY_COUNT = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second
  private static readonly ENV_ID = 'tuanzi-0gubqj98567c870e';
  private static readonly CLOUDBASE_ENDPOINT = `${this.ENV_ID}.service.tcloudbase.com`;

  // 错误统计
  private static errorStats = {
    networkErrors: 0,
    sdkErrors: 0,
    authErrors: 0,
    totalErrors: 0
  };

  private static async checkNetworkConnectivity(): Promise<boolean> {
    const endpoints = [
      `https://${this.CLOUDBASE_ENDPOINT}`,
      'https://tcb-api.tencentcloudapi.com',
      'https://www.baidu.com', // 备用检查点
    ];

    for (const endpoint of endpoints) {
      try {
        debug(`Checking connectivity to: ${endpoint}`);
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000);

        const response = await fetch(endpoint, {
          method: 'HEAD', // 使用HEAD方法减少数据传输
          signal: controller.signal,
          headers: {
            'Cache-Control': 'no-cache',
          }
        });

        clearTimeout(timeoutId);

        // 对于腾讯云端点，接受更宽泛的状态码
        const isConnected = endpoint.includes('tcloudbase.com') || endpoint.includes('tencentcloudapi.com')
          ? response.status < 500
          : response.status >= 200 && response.status < 400;

        if (isConnected) {
          debug(`Network check successful via: ${endpoint}, status: ${response.status}`);
          return true;
        }

        debug(`Network check failed for ${endpoint}, status: ${response.status}`);
      } catch (e) {
        warn(`Network connectivity check failed for ${endpoint}:`, e);
        this.errorStats.networkErrors++;
        continue; // 尝试下一个端点
      }
    }

    debug('All network connectivity checks failed');
    return false;
  }

  private static async initializeSDK(): Promise<CloudbaseApp | null> {
    try {
      debug('Initializing SDK...');

      // 验证 SDK
      if (!validateCloudbaseSDK()) {
        throw new Error('Cloudbase SDK not properly loaded');
      }

      // 设置全局对象
      if (!this.setupGlobalObjects()) {
        throw new Error('Failed to set up global objects');
      }

      // 等待确保设置生效
      await new Promise(resolve => setTimeout(resolve, 1000));

      debug('Initializing Cloudbase with ENV_ID:', this.ENV_ID);

      // 初始化 Cloudbase，使用更简化的配置
      const app = (cloudbase as any).init({
        env: this.ENV_ID,
        debug: __DEV__
      }) as CloudbaseApp;

      if (!app) {
        throw new Error('Failed to initialize Cloudbase app');
      }

      // 验证app对象的基本方法
      if (typeof app.auth !== 'function') {
        throw new Error('Cloudbase app missing auth method');
      }

      if (typeof app.callFunction !== 'function') {
        throw new Error('Cloudbase app missing callFunction method');
      }

      debug('SDK initialized successfully, available methods:', Object.getOwnPropertyNames(app).filter(prop => typeof (app as any)[prop] === 'function'));
      return app;
    } catch (e) {
      const originalError = e instanceof Error ? e : new Error(String(e));
      const cloudbaseError = new CloudbaseError(CloudbaseErrorType.SDK_INIT_ERROR, `SDK initialization failed: ${originalError.message}`, originalError);
      error('SDK initialization failed:', cloudbaseError);
      this.errorStats.sdkErrors++;
      this.errorStats.totalErrors++;
      return null;
    }
  }

  private static setupGlobalObjects(): boolean {
    debug('Setting up global objects...');
    
    // 创建基本的 location 对象
    const location: CustomLocation = {
      protocol: 'https:',
      host: this.CLOUDBASE_ENDPOINT,
      hostname: this.CLOUDBASE_ENDPOINT,
      href: `https://${this.CLOUDBASE_ENDPOINT}/`,
      origin: `https://${this.CLOUDBASE_ENDPOINT}`,
      pathname: '/',
      search: '',
      hash: '',
      port: '',
      toString: () => `https://${this.CLOUDBASE_ENDPOINT}/`
    };

    // 创建基本的 document 对象
    const document: CustomDocument = {
      createElement: (tagName: string) => ({
        style: {},
        setAttribute: () => {},
        getElementsByTagName: () => [],
        tagName: tagName.toUpperCase(),
        id: '',
        className: '',
        children: [],
        appendChild: () => {},
        removeChild: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
      }),
      getElementsByTagName: () => [],
      getElementsByClassName: () => [],
      getElementById: () => null,
      querySelector: () => null,
      querySelectorAll: () => [],
      documentElement: { 
        style: {},
        appendChild: () => {},
        removeChild: () => {} 
      },
      head: { 
        appendChild: () => {}, 
        removeChild: () => {},
        getElementsByTagName: () => []
      },
      body: { 
        appendChild: () => {}, 
        removeChild: () => {},
        getElementsByTagName: () => []
      },
      createTextNode: (text: string) => ({ nodeValue: text }),
      implementation: {
        createHTMLDocument: () => document
      }
    };

    // 创建 localStorage 对象
    const localStorage: CustomStorage = {
      getItem: async (key: string) => {
        try {
          const value = await AsyncStorage.getItem(key);
          debug('Storage get:', key, value);
          return value;
        } catch (e) {
          debug('Storage get error:', e);
          return null;
        }
      },
      setItem: async (key: string, value: string) => {
        try {
          await AsyncStorage.setItem(key, value);
          debug('Storage set:', key);
        } catch (e) {
          debug('Storage set error:', e);
        }
      },
      removeItem: async (key: string) => {
        try {
          await AsyncStorage.removeItem(key);
          debug('Storage remove:', key);
        } catch (e) {
          debug('Storage remove error:', e);
        }
      },
      clear: async () => {
        try {
          const keys = await AsyncStorage.getAllKeys();
          const cloudbaseKeys = keys.filter(key => key.startsWith('cloudbase_'));
          if (cloudbaseKeys.length > 0) {
            await AsyncStorage.multiRemove(cloudbaseKeys);
            debug('Storage cleared:', cloudbaseKeys);
          }
        } catch (e) {
          debug('Storage clear error:', e);
        }
      },
      length: 0,
      key: () => null
    };

    // 设置全局对象
    global.location = location;
    global.document = document;
    global.window = {
      location,
      document,
      localStorage,
      navigator: { 
        userAgent: 'React-Native',
        language: 'zh-CN',
        languages: ['zh-CN', 'en-US'],
        platform: 'ReactNative'
      },
      addEventListener: () => {},
      removeEventListener: () => {},
      setTimeout: global.setTimeout,
      clearTimeout: global.clearTimeout,
      setInterval: global.setInterval,
      clearInterval: global.clearInterval
    };
    global.localStorage = localStorage;

    debug('Global objects set up successfully');
    
    // 验证设置是否成功
    return validateGlobalObjects();
  }

  private static async initializeAuth(app: CloudbaseApp): Promise<boolean> {
    debug('Initializing auth provider...');
    try {
      // 简化Auth初始化，使用默认配置
      const auth = app.auth();
      debug('Result of app.auth():', auth);

      // 验证Auth对象的有效性
      if (!auth) {
        throw new Error("Auth object is null");
      }

      // 检查必要的方法是否存在
      const requiredMethods = ['anonymousAuthProvider', 'signInAnonymously', 'getLoginState', 'signOut'];
      const missingMethods = requiredMethods.filter(method => typeof (auth as any)[method] !== 'function');

      if (missingMethods.length > 0) {
        debug('Auth object is missing methods:', missingMethods);
        debug('Available auth methods:', Object.getOwnPropertyNames(auth).filter(prop => typeof (auth as any)[prop] === 'function'));

        // 如果缺少关键方法，尝试使用备用方法
        if (!auth.anonymousAuthProvider && !auth.signInAnonymously) {
          throw new Error("Auth provider not properly initialized: missing anonymous auth methods");
        }
      }

      this.auth = auth;
      debug('Auth provider initialized successfully.');
      return true;

    } catch (e: unknown) {
      const originalError = e instanceof Error ? e : new Error(String(e));
      const cloudbaseError = new CloudbaseError(CloudbaseErrorType.AUTH_INIT_ERROR, `Auth initialization failed: ${originalError.message}`, originalError);
      error("Auth provider initialization failed:", cloudbaseError);
      this.errorStats.authErrors++;
      this.errorStats.totalErrors++;
      this.auth = null;
      return false;
    }
  }

  private static async waitForInitialization(timeout: number = 5000): Promise<void> {
    const startTime = Date.now();
    while (this.isInitializing) {
      if (Date.now() - startTime > timeout) {
        throw new Error('等待初始化超时');
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  public static async getInstance(): Promise<CloudbaseApp> {
    try {
      if (this.isInitializing) {
        await this.waitForInitialization();
        if (this.instance) {
          return this.instance;
        }
      }

      if (this.instance) {
        const isConnected = await this.checkNetworkConnectivity();
        if (isConnected) {
          return this.instance;
        }
        await this.clearInstance();
      }

      this.isInitializing = true;
      debug('Starting initialization process...');

      try {
        const isConnected = await this.checkNetworkConnectivity();
        if (!isConnected) {
          throw new Error('无法连接到服务器，请检查网络连接');
        }

        const app = await this.initializeSDK();
        if (!app) {
          throw new Error('SDK initialization failed');
        }
        debug('SDK initialized, app object keys:', Object.keys(app));

        const isAuthReady = await this.initializeAuth(app);
        if (!isAuthReady) {
          throw new Error('Auth initialization failed');
        }

        this.instance = app;
        this.initRetryCount = 0;
        debug('Instance created successfully');
        return this.instance;

      } catch (e: unknown) {
        console.error("Cloudbase initialization failed:", e);
        this.initError = e instanceof Error ? e : new Error(String(e));
        
        if (this.initRetryCount < this.MAX_RETRY_COUNT) {
          const retrySuccess = await this.retryInitialization();
          if (retrySuccess && this.instance) {
            return this.instance;
          }
        }
        
        throw new Error(`Cloudbase initialization failed after ${this.initRetryCount} retries: ${this.initError.message}`);
      } finally {
        this.isInitializing = false;
      }
    } catch (e: unknown) {
      this.isInitializing = false;
      throw e;
    }
  }

  private static async retryInitialization(): Promise<boolean> {
    if (this.initRetryCount >= this.MAX_RETRY_COUNT) {
      debug('Max retry count reached');
      return false;
    }

    this.initRetryCount++;

    // 智能延迟：网络问题使用更长延迟，其他问题使用指数退避
    const baseDelay = this.initError?.message.includes('网络') || this.initError?.message.includes('连接')
      ? this.RETRY_DELAY * 3
      : this.RETRY_DELAY;
    const delay = baseDelay * Math.pow(1.5, this.initRetryCount - 1);

    debug(`Retrying initialization (attempt ${this.initRetryCount}/${this.MAX_RETRY_COUNT}) after ${delay}ms`);
    debug(`Previous error was: ${this.initError?.message}`);

    await new Promise(resolve => setTimeout(resolve, delay));

    // 清理之前的状态
    await this.clearInstance();

    try {
      // 每次重试都先检查网络
      const isConnected = await this.checkNetworkConnectivity();
      if (!isConnected) {
        debug('Network check failed during retry');
        this.initError = new Error('网络连接检查失败');
        return false;
      }

      const app = await this.initializeSDK();
      if (!app) {
        this.initError = new Error('SDK初始化失败');
        return false;
      }

      const isAuthReady = await this.initializeAuth(app);
      if (!isAuthReady) {
        this.initError = new Error('Auth初始化失败');
        return false;
      }

      this.instance = app;
      this.initError = null; // 清除错误状态
      debug(`Retry ${this.initRetryCount} succeeded`);
      return true;
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      this.initError = error;
      debug(`Retry ${this.initRetryCount} failed:`, error.message);
      return false;
    }
  }

  public static async clearInstance(): Promise<void> {
    debug('Clearing instance...');
    if (this.auth) {
      try {
        await this.auth.signOut();
        debug('Signed out successfully');
      } catch (e) {
        debug('Error during signOut:', e);
      }
    }
    this.instance = null;
    this.auth = null;
    this.initError = null;
    this.isInitializing = false;
    this.initRetryCount = 0;
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cloudbaseKeys = keys.filter(key => key.startsWith('cloudbase_'));
      if (cloudbaseKeys.length > 0) {
        await AsyncStorage.multiRemove(cloudbaseKeys);
        debug('Auth storage cleared:', cloudbaseKeys);
      }
    } catch (e) {
      debug('Error clearing auth storage:', e);
    }
  }

  public static async signOut(): Promise<void> {
    debug('Signing out...');
    await this.clearInstance();
  }

  // 获取错误统计信息
  public static getErrorStats() {
    return { ...this.errorStats };
  }

  // 重置错误统计
  public static resetErrorStats() {
    this.errorStats = {
      networkErrors: 0,
      sdkErrors: 0,
      authErrors: 0,
      totalErrors: 0
    };
  }

  // 诊断方法
  public static async diagnose(): Promise<string> {
    const stats = this.getErrorStats();
    const diagnosis = [];

    diagnosis.push(`=== Cloudbase 诊断报告 ===`);
    diagnosis.push(`时间: ${new Date().toISOString()}`);
    diagnosis.push(`环境ID: ${this.ENV_ID}`);
    diagnosis.push(`端点: ${this.CLOUDBASE_ENDPOINT}`);
    diagnosis.push(`实例状态: ${this.instance ? '已初始化' : '未初始化'}`);
    diagnosis.push(`认证状态: ${this.auth ? '已初始化' : '未初始化'}`);
    diagnosis.push(`初始化中: ${this.isInitializing ? '是' : '否'}`);
    diagnosis.push(`重试次数: ${this.initRetryCount}/${this.MAX_RETRY_COUNT}`);
    diagnosis.push(`最后错误: ${this.initError?.message || '无'}`);
    diagnosis.push(`错误统计:`);
    diagnosis.push(`  - 网络错误: ${stats.networkErrors}`);
    diagnosis.push(`  - SDK错误: ${stats.sdkErrors}`);
    diagnosis.push(`  - 认证错误: ${stats.authErrors}`);
    diagnosis.push(`  - 总错误数: ${stats.totalErrors}`);

    // 网络连接测试
    const networkStatus = await this.checkNetworkConnectivity();
    diagnosis.push(`网络连接: ${networkStatus ? '正常' : '异常'}`);

    return diagnosis.join('\n');
  }

  public static async anonymousLogin(): Promise<any> {
    if (!this.auth) {
        throw new Error('Authentication service is not ready.');
    }
    try {
        debug('Attempting anonymous login...');

        // 首先检查是否已有登录状态
        try {
          const loginState = await this.auth.getLoginState();
          if (loginState) {
              debug('User already has a login state.', loginState);
              return loginState;
          }
        } catch (e) {
          debug('Failed to get login state, proceeding with new login:', e);
        }

        let res;

        // 尝试使用新的API
        if (typeof this.auth.signInAnonymously === 'function') {
          debug('Using signInAnonymously method...');
          res = await this.auth.signInAnonymously();
        }
        // 回退到旧的API
        else if (typeof this.auth.anonymousAuthProvider === 'function') {
          debug('Using anonymousAuthProvider method...');
          res = await this.auth.anonymousAuthProvider().signIn();
        }
        else {
          throw new Error('No anonymous login method available');
        }

        if (!res) {
            throw new Error('Anonymous login failed: No response received.');
        }

        debug('Anonymous login successful:', res);
        return res;
    } catch (e) {
        const originalError = e instanceof Error ? e : new Error(String(e));
        const cloudbaseError = new CloudbaseError(CloudbaseErrorType.LOGIN_ERROR, `Anonymous login failed: ${originalError.message}`, originalError);
        error('Anonymous login failed:', cloudbaseError);
        throw cloudbaseError;
    }
  }
}

export const callFunction = async (name: string, data: object = {}): Promise<any> => {
  debug(`Calling function: ${name}`, data);
  try {
    const app = await CloudbaseManager.getInstance();
    const response = await app.callFunction({ name, data });

    if (!response || !response.result) {
      const errorMsg = `Cloud function ${name} failed: No response`;
      debug(errorMsg);
      throw new CloudbaseError(CloudbaseErrorType.FUNCTION_CALL_ERROR, errorMsg);
    }

    debug(`Function ${name} called successfully`);
    return response;
  } catch (e: unknown) {
    if (e instanceof CloudbaseError) {
      throw e; // 重新抛出已经包装的错误
    }

    const originalError = e instanceof Error ? e : new Error(String(e));
    const cloudbaseError = new CloudbaseError(CloudbaseErrorType.FUNCTION_CALL_ERROR, `Error calling function ${name}: ${originalError.message}`, originalError);
    error(`Error calling function ${name}:`, cloudbaseError);
    throw cloudbaseError;
  }
};

export default CloudbaseManager;

