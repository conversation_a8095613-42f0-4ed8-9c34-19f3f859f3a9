// src/cloudbase/index.ts
import cloudbase from "@cloudbase/js-sdk";
import "@cloudbase/js-sdk/auth";
import "@cloudbase/js-sdk/functions";
import AsyncStorage from '@react-native-async-storage/async-storage';

// 定义自定义的全局对象类型
interface CustomLocation {
  protocol: string;
  host: string;
  hostname: string;
  href: string;
  origin: string;
  pathname: string;
  search: string;
  hash: string;
  port: string;
  toString: () => string;
}

interface CustomDocument {
  createElement: (tagName: string) => {
    style: Record<string, any>;
    setAttribute: (name: string, value: string) => void;
    getElementsByTagName: () => any[];
    tagName: string;
    id: string;
    className: string;
    children: any[];
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
    addEventListener: (event: string, handler: () => void) => void;
    removeEventListener: (event: string, handler: () => void) => void;
  };
  getElementsByTagName: (tagName: string) => any[];
  getElementsByClassName: (className: string) => any[];
  getElementById: (id: string) => any;
  querySelector: (selector: string) => any;
  querySelectorAll: (selector: string) => any[];
  documentElement: {
    style: Record<string, any>;
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
  };
  head: {
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
    getElementsByTagName: (tagName: string) => any[];
  };
  body: {
    appendChild: (child: any) => void;
    removeChild: (child: any) => void;
    getElementsByTagName: (tagName: string) => any[];
  };
  createTextNode: (text: string) => { nodeValue: string };
  implementation: {
    createHTMLDocument: (title: string) => CustomDocument;
  };
}

interface CustomWindow {
  location: CustomLocation;
  document: CustomDocument;
  localStorage: CustomStorage;
  navigator: {
    userAgent: string;
    language: string;
    languages: string[];
    platform: string;
  };
  addEventListener: (event: string, handler: () => void) => void;
  removeEventListener: (event: string, handler: () => void) => void;
  setTimeout: typeof global.setTimeout;
  clearTimeout: typeof global.clearTimeout;
  setInterval: typeof global.setInterval;
  clearInterval: typeof global.clearInterval;
}

interface CustomStorage {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;
  clear: () => Promise<void>;
  length: number;
  key: (index: number) => string | null;
}

// 扩展 global 类型
declare global {
  var location: CustomLocation;
  var document: CustomDocument;
  var window: CustomWindow;
  var localStorage: CustomStorage;
}

// 扩展 cloudbase 类型
interface CloudbaseAuth {
  anonymousAuthProvider: () => {
    signIn: () => Promise<any>;
  };
  signOut: () => Promise<void>;
}

interface CloudbaseApp {
  auth: (options: any) => CloudbaseAuth;
  callFunction: (params: { name: string; data: any }) => Promise<any>;
}

// 调试日志函数
const debug = (...args: any[]) => {
  if (__DEV__) {
    console.log('[Cloudbase]', ...args);
  }
};

// 验证全局对象是否正确设置
const validateGlobalObjects = () => {
  const requiredGlobals = ['location', 'document', 'window', 'localStorage'] as const;
  const missingGlobals = requiredGlobals.filter(key => !(key in global));
  
  if (missingGlobals.length > 0) {
    debug('Missing global objects:', missingGlobals);
    return false;
  }

  if (!global.window?.document || !global.window?.location) {
    debug('Window object is missing required properties');
    return false;
  }

  return true;
};

// 验证 Cloudbase SDK 是否正确加载
const validateCloudbaseSDK = () => {
  if (typeof (cloudbase as any).init !== 'function') {
    debug('Cloudbase SDK not properly loaded: missing init method');
    return false;
  }

  if (typeof (cloudbase as any).auth !== 'function') {
    debug('Cloudbase SDK not properly loaded: missing auth module');
    return false;
  }

  return true;
};

export class CloudbaseManager {
  private static instance: CloudbaseApp | null = null;
  private static auth: CloudbaseAuth | null = null;
  private static isInitializing: boolean = false;
  private static initError: Error | null = null;
  private static initRetryCount: number = 0;
  private static readonly MAX_RETRY_COUNT = 3;
  private static readonly RETRY_DELAY = 1000; // 1 second
  private static readonly ENV_ID = 'tuanzi-0gubqj98567c870e';
  private static readonly CLOUDBASE_ENDPOINT = `${this.ENV_ID}.service.tcloudbase.com`;

  private static async checkNetworkConnectivity(): Promise<boolean> {
    try {
      debug('Checking network connectivity...');
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const response = await fetch(`https://${this.CLOUDBASE_ENDPOINT}`, {
        method: 'GET',
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      const isConnected = response.status < 500; // 任何小于500的状态码都表示可以连接
      debug(`Network check result: ${isConnected ? 'connected' : 'disconnected'}, status: ${response.status}`);
      return isConnected;
    } catch (e) {
      debug('Network connectivity check failed:', e);
      return false;
    }
  }

  private static async initializeSDK(): Promise<CloudbaseApp | null> {
    try {
      debug('Initializing SDK...');
      // 验证 SDK
      if (!validateCloudbaseSDK()) {
        throw new Error('Cloudbase SDK not properly loaded');
      }

      // 设置全局对象
      if (!this.setupGlobalObjects()) {
        throw new Error('Failed to set up global objects');
      }

      // 等待确保设置生效
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 初始化 Cloudbase
      const app = (cloudbase as any).init({
        env: this.ENV_ID,
        debug: __DEV__,
        timeout: 5000  // 设置超时时间
      }) as CloudbaseApp;

      if (!app) {
        throw new Error('Failed to initialize Cloudbase app');
      }

      debug('SDK initialized successfully');
      return app;
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      debug('SDK initialization failed:', error.message);
      return null;
    }
  }

  private static setupGlobalObjects(): boolean {
    debug('Setting up global objects...');
    
    // 创建基本的 location 对象
    const location: CustomLocation = {
      protocol: 'https:',
      host: 'tuanzi-6g2a87g673c68379.service.tcloudbase.com',
      hostname: 'tuanzi-6g2a87g673c68379.service.tcloudbase.com',
      href: 'https://tuanzi-6g2a87g673c68379.service.tcloudbase.com/',
      origin: 'https://tuanzi-6g2a87g673c68379.service.tcloudbase.com',
      pathname: '/',
      search: '',
      hash: '',
      port: '',
      toString: () => 'https://tuanzi-6g2a87g673c68379.service.tcloudbase.com/'
    };

    // 创建基本的 document 对象
    const document: CustomDocument = {
      createElement: (tagName: string) => ({
        style: {},
        setAttribute: () => {},
        getElementsByTagName: () => [],
        tagName: tagName.toUpperCase(),
        id: '',
        className: '',
        children: [],
        appendChild: () => {},
        removeChild: () => {},
        addEventListener: () => {},
        removeEventListener: () => {},
      }),
      getElementsByTagName: () => [],
      getElementsByClassName: () => [],
      getElementById: () => null,
      querySelector: () => null,
      querySelectorAll: () => [],
      documentElement: { 
        style: {},
        appendChild: () => {},
        removeChild: () => {} 
      },
      head: { 
        appendChild: () => {}, 
        removeChild: () => {},
        getElementsByTagName: () => []
      },
      body: { 
        appendChild: () => {}, 
        removeChild: () => {},
        getElementsByTagName: () => []
      },
      createTextNode: (text: string) => ({ nodeValue: text }),
      implementation: {
        createHTMLDocument: () => document
      }
    };

    // 创建 localStorage 对象
    const localStorage: CustomStorage = {
      getItem: async (key: string) => {
        try {
          const value = await AsyncStorage.getItem(key);
          debug('Storage get:', key, value);
          return value;
        } catch (e) {
          debug('Storage get error:', e);
          return null;
        }
      },
      setItem: async (key: string, value: string) => {
        try {
          await AsyncStorage.setItem(key, value);
          debug('Storage set:', key);
        } catch (e) {
          debug('Storage set error:', e);
        }
      },
      removeItem: async (key: string) => {
        try {
          await AsyncStorage.removeItem(key);
          debug('Storage remove:', key);
        } catch (e) {
          debug('Storage remove error:', e);
        }
      },
      clear: async () => {
        try {
          const keys = await AsyncStorage.getAllKeys();
          const cloudbaseKeys = keys.filter(key => key.startsWith('cloudbase_'));
          if (cloudbaseKeys.length > 0) {
            await AsyncStorage.multiRemove(cloudbaseKeys);
            debug('Storage cleared:', cloudbaseKeys);
          }
        } catch (e) {
          debug('Storage clear error:', e);
        }
      },
      length: 0,
      key: () => null
    };

    // 设置全局对象
    global.location = location;
    global.document = document;
    global.window = {
      location,
      document,
      localStorage,
      navigator: { 
        userAgent: 'React-Native',
        language: 'zh-CN',
        languages: ['zh-CN', 'en-US'],
        platform: 'ReactNative'
      },
      addEventListener: () => {},
      removeEventListener: () => {},
      setTimeout: global.setTimeout,
      clearTimeout: global.clearTimeout,
      setInterval: global.setInterval,
      clearInterval: global.clearInterval
    };
    global.localStorage = localStorage;

    debug('Global objects set up successfully');
    
    // 验证设置是否成功
    return validateGlobalObjects();
  }

  private static async initializeAuth(app: CloudbaseApp): Promise<boolean> {
    debug('Initializing auth provider...');
    try {
      const auth = app.auth({
        persistence: "local",
        getStorageSync: async (key: string) => {
          try {
            const value = await AsyncStorage.getItem(key);
            debug('Auth storage get:', key, value);
            return value;
          } catch (e) {
            debug('Auth storage get error:', e);
            return null;
          }
        },
        setStorageSync: async (key: string, value: string) => {
          try {
            await AsyncStorage.setItem(key, value);
            debug('Auth storage set:', key);
          } catch (e) {
            debug('Auth storage set error:', e);
            throw e;
          }
        },
        removeStorageSync: async (key: string) => {
          try {
            await AsyncStorage.removeItem(key);
            debug('Auth storage remove:', key);
          } catch (e) {
            debug('Auth storage remove error:', e);
            throw e;
          }
        },
        clearStorageSync: async () => {
          try {
            const keys = await AsyncStorage.getAllKeys();
            const cloudbaseKeys = keys.filter(key => key.startsWith('cloudbase_'));
            if (cloudbaseKeys.length > 0) {
              await AsyncStorage.multiRemove(cloudbaseKeys);
              debug('Auth storage cleared:', cloudbaseKeys);
            }
          } catch (e) {
            debug('Auth storage clear error:', e);
            throw e;
          }
        }
      });
      debug('Result of app.auth():', auth);

      if (!auth || typeof auth.anonymousAuthProvider !== 'function') {
        debug('Auth object is invalid. Keys:', auth ? Object.keys(auth) : 'null');
        throw new Error("Auth provider not properly initialized");
      }

      this.auth = auth;
      debug('Auth provider initialized successfully.');
      return true;

    } catch (e: unknown) {
      console.error("Auth provider initialization failed:", e);
      if (e instanceof Error) {
        debug("Auth error details:", e.message, e.stack);
      }
      this.auth = null;
      return false;
    }
  }

  private static async waitForInitialization(timeout: number = 5000): Promise<void> {
    const startTime = Date.now();
    while (this.isInitializing) {
      if (Date.now() - startTime > timeout) {
        throw new Error('等待初始化超时');
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  public static async getInstance(): Promise<CloudbaseApp> {
    try {
      if (this.isInitializing) {
        await this.waitForInitialization();
        if (this.instance) {
          return this.instance;
        }
      }

      if (this.instance) {
        const isConnected = await this.checkNetworkConnectivity();
        if (isConnected) {
          return this.instance;
        }
        await this.clearInstance();
      }

      this.isInitializing = true;
      debug('Starting initialization process...');

      try {
        const isConnected = await this.checkNetworkConnectivity();
        if (!isConnected) {
          throw new Error('无法连接到服务器，请检查网络连接');
        }

        const app = await this.initializeSDK();
        if (!app) {
          throw new Error('SDK initialization failed');
        }
        debug('SDK initialized, app object keys:', Object.keys(app));

        const isAuthReady = await this.initializeAuth(app);
        if (!isAuthReady) {
          throw new Error('Auth initialization failed');
        }

        this.instance = app;
        this.initRetryCount = 0;
        debug('Instance created successfully');
        return this.instance;

      } catch (e: unknown) {
        console.error("Cloudbase initialization failed:", e);
        this.initError = e instanceof Error ? e : new Error(String(e));
        
        if (this.initRetryCount < this.MAX_RETRY_COUNT) {
          const retrySuccess = await this.retryInitialization();
          if (retrySuccess && this.instance) {
            return this.instance;
          }
        }
        
        throw new Error(`Cloudbase initialization failed after ${this.initRetryCount} retries: ${this.initError.message}`);
      } finally {
        this.isInitializing = false;
      }
    } catch (e: unknown) {
      this.isInitializing = false;
      throw e;
    }
  }

  private static async retryInitialization(): Promise<boolean> {
    if (this.initRetryCount >= this.MAX_RETRY_COUNT) {
      debug('Max retry count reached');
      return false;
    }

    this.initRetryCount++;
    const delay = this.RETRY_DELAY * Math.pow(2, this.initRetryCount - 1);
    debug(`Retrying initialization (attempt ${this.initRetryCount}/${this.MAX_RETRY_COUNT}) after ${delay}ms`);
    
    await new Promise(resolve => setTimeout(resolve, delay));
    await this.clearInstance();

    try {
      // 每次重试都先检查网络
      const isConnected = await this.checkNetworkConnectivity();
      if (!isConnected) {
        debug('Network check failed during retry');
        return false;
      }

      const app = await this.initializeSDK();
      if (!app) {
        return false;
      }

      const isAuthReady = await this.initializeAuth(app);
      if (!isAuthReady) {
        return false;
      }

      this.instance = app;
      return true;
    } catch (e) {
      debug(`Retry ${this.initRetryCount} failed:`, e);
      return false;
    }
  }

  public static async clearInstance(): Promise<void> {
    debug('Clearing instance...');
    if (this.auth) {
      try {
        await this.auth.signOut();
        debug('Signed out successfully');
      } catch (e) {
        debug('Error during signOut:', e);
      }
    }
    this.instance = null;
    this.auth = null;
    this.initError = null;
    this.isInitializing = false;
    this.initRetryCount = 0;
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cloudbaseKeys = keys.filter(key => key.startsWith('cloudbase_'));
      if (cloudbaseKeys.length > 0) {
        await AsyncStorage.multiRemove(cloudbaseKeys);
        debug('Auth storage cleared:', cloudbaseKeys);
      }
    } catch (e) {
      debug('Error clearing auth storage:', e);
    }
  }

  public static async signOut(): Promise<void> {
    debug('Signing out...');
    await this.clearInstance();
  }

  public static async anonymousLogin(): Promise<any> {
    if (!this.auth) {
        throw new Error('Authentication service is not ready.');
    }
    try {
        debug('Attempting anonymous login...');
        const loginState = await this.auth.getLoginState();
        if (loginState) {
            debug('User already has a login state.', loginState);
            return loginState;
        }
        const res = await this.auth.anonymousAuthProvider().signIn();
        if (!res) {
            throw new Error('Anonymous login failed: No response received.');
        }
        debug('Anonymous login successful.');
        return res;
    } catch (e) {
        console.error('Anonymous login failed:', e);
        throw e;
    }
  }
}

export const callFunction = async (name: string, data: object = {}): Promise<any> => {
  debug(`Calling function: ${name}`, data);
  try {
    const app = await CloudbaseManager.getInstance();
    const response = await app.callFunction({ name, data });
    
    if (!response || !response.result) {
      debug(`Function ${name} failed: No response`);
      throw new Error(`Cloud function ${name} failed: No response`);
    }
    
    return response;
  } catch (e: unknown) {
    console.error(`Error calling function ${name}:`, e);
    throw e;
  }
};

export default CloudbaseManager;

