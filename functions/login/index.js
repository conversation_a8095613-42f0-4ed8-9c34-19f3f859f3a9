/**
 * 云函数：login
 * * 功能：处理用户登录与注册的核心逻辑。
 * 当被前端调用时，此函数会自动获取用户的微信OpenID。
 * 它会查询'users'集合，判断该用户是否为新用户。
 * - 如果是新用户，则在'users'集合中创建一条新记录。
 * - 如果是老用户，则更新其最后登录时间。
 * 最终，返回完整的用户信息给前端。
 * * @param {object} event - 前端调用时传递的参数。我们期望其中包含userInfo对象。
 * @param {object} context - 包含运行时信息的上下文对象。
 * @returns {object} - 返回一个包含用户数据库记录的对象。
 */
const cloud = require('wx-server-sdk');

// 初始化 cloud
cloud.init({
  // API 调用都保持和云函数当前所在环境一致
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 云函数入口函数
exports.main = async (event, context) => {
  // 从调用信息中获取 OPENID
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  // 从'users'集合中查询该openid的记录
  const userRecord = await db.collection('users').where({
    _openid: openid
  }).get();

  // event.userInfo是从前端APP通过微信授权后获取的用户信息
  const userInfo = event.userInfo; 

  if (userRecord.data.length === 0) {
    // 如果记录为空，说明是新用户
    console.log('新用户, openid:', openid);
    try {
      // 创建新用户记录
      await db.collection('users').add({
        _openid: openid,
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        createdAt: new Date(),
        lastLoginAt: new Date(),
      });
    } catch (e) {
      console.error('创建新用户失败', e);
      // 可以根据需要返回错误信息
      return {
          code: 500,
          message: '创建新用户失败',
          error: e
      };
    }
  } else {
    // 如果是老用户，更新最后登录时间
    console.log('老用户, openid:', openid);
    try {
      await db.collection('users').doc(userRecord.data[0]._id).update({
        data: {
          lastLoginAt: new Date(),
          // 如果用户更换了头像或昵称，也在此更新
          nickName: userInfo.nickName,
          avatarUrl: userInfo.avatarUrl,
        }
      });
    } catch (e) {
        console.error('更新老用户失败', e);
         return {
            code: 500,
            message: '更新老用户失败',
            error: e
        };
    }
  }
  
  // 重新获取用户信息并返回给前端
  const finalUserRecord = await db.collection('users').where({
      _openid: openid
  }).get();

  return {
      code: 200,
      message: '登录成功',
      data: finalUserRecord.data[0]
  };
};

