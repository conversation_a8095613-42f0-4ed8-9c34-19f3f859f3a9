// =================================================================
// 文件: cloudfunctions/startGame/index.js
// 描述: 房主开始游戏。
// =================================================================
const cloud = require('wx-server-sdk');
const { ROOMS_COLLECTION } = require('../utils/config');
const { getRandomWord } = require('../utils/words');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();

/**
 * package.json 依赖:
 * {
 * "dependencies": {
 * "wx-server-sdk": "~2.6.3"
 * }
 * }
 */

exports.main = async (event, context) => {
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    const { roomId } = event;

    try {
        const roomDoc = db.collection(ROOMS_COLLECTION).doc(roomId);
        const roomSnapshot = await roomDoc.get();
        const room = roomSnapshot.data;

        if (room.hostId !== openid) {
            return { success: false, message: '只有房主才能开始游戏' };
        }
        if (room.players.length < 2) {
            return { success: false, message: '至少需要2名玩家才能开始游戏' };
        }

        const firstDrawer = room.players[0].openid;
        const word = getRandomWord();

        await roomDoc.update({
            data: {
                status: 'playing',
                'gameData.currentWord': word,
                'gameData.currentDrawer': firstDrawer,
                'gameData.startTime': new Date(),
                'gameData.guesses': []
            }
        });

        return { success: true, message: '游戏开始！' };

    } catch (e) {
        console.error('startGame function error:', e);
        return { success: false, message: e.message };
    }
};

