// =================================================================
// 文件: cloudfunctions/joinRoom/index.js
// 描述: 玩家加入一个已存在的房间。
// =================================================================
const cloud = require('wx-server-sdk');
const { ROOMS_COLLECTION } = require('../utils/config');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;

/**
 * package.json 依赖:
 * {
 * "dependencies": {
 * "wx-server-sdk": "~2.6.3"
 * }
 * }
 */

exports.main = async (event, context) => {
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    const { roomId, playerInfo } = event;

    if (!roomId || !playerInfo || !playerInfo.nickName || !playerInfo.avatarUrl) {
        return { success: false, message: '缺少房间ID或玩家信息' };
    }

    try {
        const roomDoc = db.collection(ROOMS_COLLECTION).doc(roomId);
        const roomSnapshot = await roomDoc.get();
        const room = roomSnapshot.data;

        if (!room) {
            return { success: false, message: '房间不存在' };
        }
        if (room.status !== 'waiting') {
            return { success: false, message: '游戏已经开始或已结束，无法加入' };
        }
        if (room.players.some(p => p.openid === openid)) {
            return { success: true, message: '你已在房间中', roomData: room };
        }
        
        const newPlayer = {
            openid: openid,
            nickName: playerInfo.nickName,
            avatarUrl: playerInfo.avatarUrl,
            score: 0
        };

        await roomDoc.update({
            data: {
                players: _.push(newPlayer)
            }
        });

        const updatedRoom = await roomDoc.get();
        return {
            success: true,
            message: '加入房间成功',
            roomData: updatedRoom.data
        };

    } catch (e) {
        console.error('joinRoom function error:', e);
        return {
            success: false,
            message: e.message,
        };
    }
};

