// =================================================================
// 文件: cloudfunctions/createRoom/index.js
// 描述: 创建一个新的游戏房间。
// =================================================================
const cloud = require('wx-server-sdk');
const { ROOMS_COLLECTION } = require('../utils/config');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();

/**
 * package.json 依赖:
 * {
 * "dependencies": {
 * "wx-server-sdk": "~2.6.3"
 * }
 * }
 */

exports.main = async (event, context) => {
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    const { playerInfo } = event;

    if (!playerInfo || !playerInfo.nickName || !playerInfo.avatarUrl) {
        return { success: false, message: '创建房间失败，缺少玩家信息。' };
    }

    try {
        const newRoom = {
            hostId: openid,
            status: "waiting", // waiting | playing | judging | finished
            players: [{
                openid: openid,
                nickName: playerInfo.nickName,
                avatarUrl: playerInfo.avatarUrl,
                score: 0
            }],
            gameData: {
                currentWord: "",
                currentDrawer: "",
                startTime: null,
                guesses: []
            },
            createdAt: new Date()
        };

        const addResult = await db.collection(ROOMS_COLLECTION).add({
            data: newRoom
        });

        return {
            success: true,
            message: '房间创建成功',
            roomId: addResult._id
        };

    } catch (e) {
        console.error('createRoom function error:', e);
        return {
            success: false,
            message: e.message,
        };
    }
};

