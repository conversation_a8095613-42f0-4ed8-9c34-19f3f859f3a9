// =================================================================
// 文件: cloudfunctions/judgeAndScore/index.js
// 描述: 绘画者判定答案，系统计分并开启下一轮。
// =================================================================
const cloud = require('wx-server-sdk');
const { ROOMS_COLLECTION } = require('../utils/config');
const { getRandomWord } = require('../utils/words');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();

/**
 * package.json 依赖:
 * {
 * "dependencies": {
 * "wx-server-sdk": "~2.6.3"
 * }
 * }
 */

exports.main = async (event, context) => {
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    const { roomId, correctGuesses } = event; // correctGuesses: 包含了猜对玩家openid的数组

    try {
        const roomDoc = db.collection(ROOMS_COLLECTION).doc(roomId);
        const roomSnapshot = await roomDoc.get();
        const room = roomSnapshot.data;

        if (room.gameData.currentDrawer !== openid) {
            return { success: false, message: '只有当前绘画者才能进行判定' };
        }

        // --- 计分 ---
        const updatedPlayers = room.players.map(player => {
            if (correctGuesses.includes(player.openid)) {
                player.score += 2; // 猜对者+2分
            }
            if (player.openid === room.gameData.currentDrawer) {
                player.score += correctGuesses.length * 1; // 绘画者根据猜对人数加分
            }
            return player;
        });

        // --- 准备下一轮 ---
        const drawerIndex = room.players.findIndex(p => p.openid === room.gameData.currentDrawer);
        const nextDrawerIndex = (drawerIndex + 1) % room.players.length;
        
        // 如果一轮结束 (回到了第一个人)
        if (nextDrawerIndex === 0) {
             await roomDoc.update({
                data: {
                    status: 'finished',
                    players: updatedPlayers,
                }
            });
            return { success: true, message: '所有玩家已轮换完毕，游戏结束！' };
        }

        // 继续下一轮
        const nextDrawer = room.players[nextDrawerIndex].openid;
        const nextWord = getRandomWord();
        await roomDoc.update({
            data: {
                status: 'playing',
                players: updatedPlayers,
                'gameData.currentWord': nextWord,
                'gameData.currentDrawer': nextDrawer,
                'gameData.startTime': new Date(),
                'gameData.guesses': [],
            }
        });

        return { success: true, message: '计分完成，下一轮开始！' };

    } catch (e) {
        console.error('judgeAndScore function error:', e);
        return { success: false, message: e.message };
    }
};

