// =================================================================
// 文件: cloudfunctions/submitGuess/index.js
// 描述: 玩家提交猜测的答案。
// =================================================================
const cloud = require('wx-server-sdk');
const { ROOMS_COLLECTION } = require('../utils/config');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;

/**
 * package.json 依赖:
 * {
 * "dependencies": {
 * "wx-server-sdk": "~2.6.3"
 * }
 * }
 */

exports.main = async (event, context) => {
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    const { roomId, guess, playerInfo } = event;
    
    try {
        const roomSnapshot = await db.collection(ROOMS_COLLECTION).doc(roomId).get();
        const room = roomSnapshot.data;

        if (room.gameData.currentDrawer === openid) {
            return { success: false, message: '绘画者不能猜测' };
        }

        const newGuess = {
            openid: openid,
            nickName: playerInfo.nickName,
            guess: guess,
            timestamp: new Date()
        };

        await db.collection(ROOMS_COLLECTION).doc(roomId).update({
            data: {
                'gameData.guesses': _.push(newGuess)
            }
        });

        return { success: true, message: '提交成功' };

    } catch (e) {
        console.error('submitGuess function error:', e);
        return { success: false, message: e.message };
    }
};

