// =================================================================
// 文件: cloudfunctions/leaveRoom/index.js
// 描述: 玩家离开或解散房间。
// =================================================================
const cloud = require('wx-server-sdk');
const { ROOMS_COLLECTION } = require('../utils/config');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});
const db = cloud.database();
const _ = db.command;

/**
 * package.json 依赖:
 * {
 * "dependencies": {
 * "wx-server-sdk": "~2.6.3"
 * }
 * }
 */

exports.main = async (event, context) => {
    const wxContext = cloud.getWXContext();
    const openid = wxContext.OPENID;
    const { roomId } = event;

    try {
        const roomDoc = db.collection(ROOMS_COLLECTION).doc(roomId);
        const roomSnapshot = await roomDoc.get();
        const room = roomSnapshot.data;

        if (!room) {
            return { success: false, message: '房间不存在' };
        }

        // 如果是房主离开，则解散房间
        if (room.hostId === openid) {
            await roomDoc.remove();
            return { success: true, message: '房主已解散房间' };
        }

        // 如果是普通玩家离开
        const remainingPlayers = room.players.filter(p => p.openid !== openid);

        if (remainingPlayers.length === 0) {
            // 如果是最后一位玩家离开，也解散房间
            await roomDoc.remove();
            return { success: true, message: '最后一位玩家已离开，房间已解散' };
        }

        const updateData = {
            players: remainingPlayers
        };

        // 如果离开的玩家是当前绘画者，需要移交绘画权给下一位玩家
        if(room.gameData.currentDrawer === openid && room.status === 'playing') {
            const drawerIndex = room.players.findIndex(p => p.openid === openid);
            const nextDrawerIndex = (drawerIndex + 1) % remainingPlayers.length; // 在剩余玩家中找
            updateData['gameData.currentDrawer'] = remainingPlayers[nextDrawerIndex].openid;
        }


        await roomDoc.update({
            data: updateData
        });

        return { success: true, message: '已成功离开房间' };

    } catch (e) {
        console.error('leaveRoom function error:', e);
        return {
            success: false,
            message: e.message,
        };
    }
};

