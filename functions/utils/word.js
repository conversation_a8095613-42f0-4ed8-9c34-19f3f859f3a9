// =================================================================
// 文件: cloudfunctions/utils/words.js
// 描述: "你画我猜"游戏的词库。MVP阶段可以先用一个简单的列表。
// =================================================================
const wordList = [
    '太阳', '月亮', '星星', '电脑', '手机', '键盘', '鼠标', '椅子',
    '桌子', '杯子', '西瓜', '苹果', '香蕉', '猫', '狗', '鸟', '鱼',
    '篮球', '足球', '地球', '冰淇淋', '火车', '飞机', '自行车', '风筝'
];

/**
 * 从词库中随机获取一个词语
 * @returns {string} 一个随机词语
 */
function getRandomWord() {
    const index = Math.floor(Math.random() * wordList.length);
    return wordList[index];
}

module.exports = {
    getRandomWord,
};

