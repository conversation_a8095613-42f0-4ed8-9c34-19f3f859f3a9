# 团子 (<PERSON><PERSON><PERSON>)

> ### 标语：我们团，你们玩

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
![Version](https://img.shields.io/badge/version-0.1.0--alpha-blue)

一个专为线下聚会、团建活动设计的集成式娱乐辅助APP。

---

### 📖 项目简介

“团子”致力于解决线下聚会（如朋友小聚、班级团建、公司破冰）中“玩什么”、“怎么玩”的难题。它不是一个线上社交软件，而是一个线下活动的“数字工具箱”，通过手机APP来组织和辅助面对面的互动游戏，让每一次相聚都充满欢声笑语。

### ✨ 项目愿景

我们长远的目标是打造一个高度灵活和可扩展的团建活动平台。未来的“团子”将拥有强大的“环节”设计器，允许主持人像拼接乐高一样自由组合各种游戏与流程，并支持更丰富的游戏库（包括“谁是卧底”、“狼人杀”等经典游戏），最终形成一个可以分享和交流活动创意的社区。

### 🚀 当前状态: MVP 开发中

项目目前正处于紧张的 **MVP (最小可行产品)** 开发阶段。当前版本的核心目标是验证主要玩法，因此功能相对精简。

**MVP 版本将包含以下核心功能：**
* 📱 **跨平台APP**: 基于 React Native，未来可同时支持安卓和iOS。
* 🚪 **房间系统**: 支持通过房间号快速创建和加入游戏。
* 🎨 **核心游戏**: 内置经典互动游戏 **“你画我猜”**。
* 💯 **实时同步**: 游戏画板、流程与分数排行榜实时更新。
* ☁️ **云端后台**: 基于腾讯云开发 CloudBase，保障国内用户的流畅体验。

### 🛠️ 核心技术栈

* **前端**: `React Native`
* **后端**: `腾讯云开发 CloudBase`
* **数据库**: `CloudBase 云数据库`
* **核心逻辑**: `CloudBase 云函数`

### 🚀 如何开始

1.  **克隆仓库**
    ```bash
    git clone https://github.com/oneder2/TeamZone.git
    cd tuanzi
    ```
2.  **安装依赖**
    ```bash
    npm install
    ```
3.  **配置云开发环境**
    - 前往腾讯云开发官网注册并创建一个新环境。
    - 在项目根目录创建 `cloudbaserc.json` 文件，并填入你的环境ID等信息。

4.  **运行项目 (Android)**
    ```bash
    npx react-native run-android
    ```

### 🤝 贡献代码

我们非常欢迎对这个项目感兴趣的开发者参与贡献！

1.  Fork 本仓库
2.  创建你的 Feature Branch (`git checkout -b feature/AmazingFeature`)
3.  Commit 你的改动 (`git commit -m 'Add some AmazingFeature'`)
4.  Push 到 Branch (`git push origin feature/AmazingFeature`)
5.  提交一个 Pull Request

### 团队

* **开发者A (项目发起人)**
* **开发者B**
* **设计师 (美术顾问)**

